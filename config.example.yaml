# DunshanOps 服务器配置文件

# 服务器配置
server:
  # 服务器监听地址 Default: 0.0.0.0
  #host: 0.0.0.0        
  # 服务器端口 Default: 8080
  #port: 8080        
  # 服务器模式 Default: release
  #mode: release       

# 日志配置
log:
  # 日志级别 Default: info
  #level: info    
  # 日志文件路径 Default: logs/dunshanops.log
  #file_path: "logs/dunshanops.log"  
  # 单个日志文件最大大小(MB) Default: 10
  #max_size: 10         
  # 最大备份文件数 Default: 3
  #max_backups: 3       
  # 日志文件最大保留天数 Default: 28
  #max_age: 28         
  # 是否压缩备份文件 Default: true
  #compress: true

# 数据库配置
database:
  # MongoDB 连接 URI (必须指定)
  uri: "mongodb://localhost:27017"
  # 连接超时时间(秒) Default: 10
  #timeout: 10
  # 是否开启认证 Default: false
  #auth: false
  # 用户名 (当 auth: true 时必须指定)
  #username: "dunshanops_user"
  # 密码 (当 auth: true 时必须指定)
  #password: "your_password"
