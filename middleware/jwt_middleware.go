// Package middleware 提供 JWT 认证中间件
package middleware

import (
	"DunshanOps/logger"
	"DunshanOps/pkg/jwt"
	"DunshanOps/pkg/response"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// JWTAuth JWT 认证中间件
// 从请求头中解析 JWT token，验证有效性并提取用户信息
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取 Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.APPLogger.Warn().
				Str("operation", "jwt_auth").
				Msg("缺少 Authorization 请求头")
			c.JSON(http.StatusUnauthorized, response.ErrorResponse(401, "未提供认证令牌"))
			c.Abort()
			return
		}

		// 检查 Bearer 前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			logger.APPLogger.Warn().
				Str("operation", "jwt_auth").
				Str("authHeader", authHeader).
				Msg("Authorization 头格式错误")
			c.JSON(http.StatusUnauthorized, response.ErrorResponse(401, "认证令牌格式错误"))
			c.Abort()
			return
		}

		// 提取 token
		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			logger.APPLogger.Warn().
				Str("operation", "jwt_auth").
				Msg("认证令牌为空")
			c.JSON(http.StatusUnauthorized, response.ErrorResponse(401, "认证令牌为空"))
			c.Abort()
			return
		}

		// 解析和验证 token
		claims, err := jwt.ParseToken(tokenString, "")
		if err != nil {
			logger.APPLogger.Warn().
				Str("operation", "jwt_auth").
				Err(err).
				Msg("认证令牌验证失败")
			c.JSON(http.StatusUnauthorized, response.ErrorResponse(401, "认证令牌无效或已过期"))
			c.Abort()
			return
		}

		// 将用户ID存储到上下文中
		c.Set("userID", claims.UserID)

		// 记录认证成功日志
		logger.APPLogger.Debug().
			Str("operation", "jwt_auth").
			Str("userId", claims.UserID.Hex()).
			Msg("JWT 认证成功")

		// 继续处理请求
		c.Next()
	}
}

// GetUserIDFromContext 从 Gin 上下文中获取用户ID
// 返回 primitive.ObjectID 类型的用户ID
func GetUserIDFromContext(c *gin.Context) (primitive.ObjectID, bool) {
	userID, exists := c.Get("userID")
	if !exists {
		return primitive.NilObjectID, false
	}

	objectID, ok := userID.(primitive.ObjectID)
	if !ok {
		return primitive.NilObjectID, false
	}

	return objectID, true
}
