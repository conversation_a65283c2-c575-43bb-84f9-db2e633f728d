// Package handlers 认证相关的 HTTP 处理器
package handlers

import (
	"DunshanOps/logger"
	"DunshanOps/models"
	"DunshanOps/pkg/response"
	"DunshanOps/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器结构体
type AuthHandler struct {
	authService *services.AuthService // 认证服务实例
}

// NewAuthHandler 创建新的认证处理器实例
func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		authService: services.NewAuthService(),
	}
}

// Login 处理用户登录请求
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "user_login").
			Err(err).
			Msg("登录请求参数解析失败")
		c.<PERSON>(http.StatusBadRequest, response.ErrorResponse(400, "请求参数格式错误"))
		return
	}

	// 调用认证服务进行登录验证
	resp, err := h.authService.Login(req.Username, req.Password, req.AuthType)
	if err != nil {
		logger.APPLogger.Warn().
			Str("operation", "user_login").
			Str("username", req.Username).
			Str("authType", string(req.AuthType)).
			Err(err).
			Msg("用户登录失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, err.Error()))
		return
	}

	// 记录登录成功日志
	logger.APPLogger.Info().
		Str("operation", "user_login").
		Str("username", req.Username).
		Str("authType", string(req.AuthType)).
		Msg("用户登录成功")

	// 返回登录成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(resp))
}

// GetProviders 获取可用的认证提供者列表
func (h *AuthHandler) GetProviders(c *gin.Context) {
	providers := h.authService.GetEnabledProviders()
	c.JSON(http.StatusOK, response.SuccessResponse(gin.H{
		"providers": providers,
	}))
}
