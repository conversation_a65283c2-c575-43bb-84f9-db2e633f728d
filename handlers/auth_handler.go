// Package handlers 认证相关的 HTTP 处理器
package handlers

import (
	"DunshanOps/logger"
	"DunshanOps/middleware"
	"DunshanOps/models"
	"DunshanOps/pkg/response"
	"DunshanOps/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器结构体
type AuthHandler struct {
	authService *services.AuthService // 认证服务实例
	userService *services.UserService // 用户服务实例
}

// NewAuthHandler 创建新的认证处理器实例
func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		authService: services.NewAuthService(),
		userService: services.NewUserService(),
	}
}

// Login 处理用户登录请求
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "user_login").
			Err(err).
			Msg("登录请求参数解析失败")
		c.<PERSON>(http.StatusBadRequest, response.ErrorResponse(400, "请求参数格式错误"))
		return
	}

	// 调用认证服务进行登录验证
	resp, err := h.authService.Login(req.Username, req.Password, req.AuthType)
	if err != nil {
		logger.APPLogger.Warn().
			Str("operation", "user_login").
			Str("username", req.Username).
			Str("authType", string(req.AuthType)).
			Err(err).
			Msg("用户登录失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, err.Error()))
		return
	}

	// 记录登录成功日志
	logger.APPLogger.Info().
		Str("operation", "user_login").
		Str("username", req.Username).
		Str("authType", string(req.AuthType)).
		Msg("用户登录成功")

	// 返回登录成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(resp))
}

// GetProviders 获取可用的认证提供者列表
func (h *AuthHandler) GetProviders(c *gin.Context) {
	providers := h.authService.GetEnabledProviders()
	c.JSON(http.StatusOK, response.SuccessResponse(gin.H{
		"providers": providers,
	}))
}

// GetCurrentUser 获取当前登录用户信息
// 从 JWT token 中解析用户ID，然后查询并返回用户信息
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	// 从上下文中获取用户ID
	userIDString, exists := middleware.GetUserIDStringFromContext(c)
	if !exists {
		logger.APPLogger.Error().
			Str("operation", "get_current_user").
			Msg("无法从上下文中获取用户ID")
		c.JSON(http.StatusInternalServerError, response.ErrorResponse(500, "获取用户信息失败：内部错误"))
		return
	}

	// 调用用户服务获取用户信息
	user, err := h.userService.GetUserByID(userIDString)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_current_user").
			Str("userId", userIDString).
			Err(err).
			Msg("获取当前用户信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "获取用户信息失败："+err.Error()))
		return
	}

	// 记录查询成功日志
	logger.APPLogger.Info().
		Str("operation", "get_current_user").
		Str("userId", userIDString).
		Str("username", user.Username).
		Msg("当前用户信息查询成功")

	// 返回用户信息
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}
