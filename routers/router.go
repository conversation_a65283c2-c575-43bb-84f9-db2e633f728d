// Package routers 路由管理中心，统一管理所有路由配置
package routers

import (
	"github.com/gin-gonic/gin"
)

// InitRoutes 初始化所有路由配置
// r: gin 引擎实例
func InitRoutes(r *gin.Engine) {
	// 设置系统路由（健康检查、根路径等）
	SetupSystemRoutes(r)

	// 设置 API 路由组
	api := r.Group("/api")
	{
		// 认证相关路由
		SetupAuthRoutes(api)

		// 用户管理路由
		SetupUserRoutes(api)

		// 用户组管理路由
		SetupUserGroupRoutes(api)

		// 其他业务路由可以在这里添加
		// SetupProjectRoutes(api)  // 项目管理路由
		// SetupMonitorRoutes(api)  // 监控相关路由
		// SetupDeployRoutes(api)   // 部署相关路由
		// SetupConfigRoutes(api)   // 配置管理路由
	}
}
