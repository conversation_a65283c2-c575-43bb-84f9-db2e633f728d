// Package routers 定义系统相关路由
package routers

import (
	"net/http"

	"DunshanOps/config"
	"DunshanOps/database"

	"github.com/gin-gonic/gin"
)

// SetupSystemRoutes 设置系统相关路由
// r: gin 引擎实例
func SetupSystemRoutes(r *gin.Engine) {
	// 健康检查端点
	// GET /health - 系统健康检查接口
	r.GET("/health", func(c *gin.Context) {
		// 检查数据库连接状态
		dbStatus := "ok"
		if err := database.HealthCheck(); err != nil {
			dbStatus = "error: " + err.Error()
		}

		// 返回系统状态信息
		c.JSON(http.StatusOK, gin.H{
			"status":      "ok",                       // 服务状态
			"message":     "DunshanOps 服务器运行正常",       // 状态消息
			"server_host": config.AppConf.Server.Host, // 服务器主机
			"server_port": config.AppConf.Server.Port, // 服务器端口
			"server_mode": config.GetServerMode(),     // 服务器模式
			"debug_mode":  config.IsDebug(),           // 是否调试模式
			"database":    dbStatus,                   // 数据库状态
		})
	})

	// 根路径端点
	// GET / - 服务器根路径，返回基本信息和可用接口列表
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "欢迎使用 DunshanOps 服务器", // 欢迎消息
			"version": "1.0.0",               // 版本信息
			"endpoints": []string{ // 可用接口列表
				"GET /health - 健康检查",
				"GET / - 根路径",
				"POST /api/auth/login - 用户登录",
				"GET /api/auth/providers - 获取认证提供者",
			},
		})
	})
}
