// Package routers 定义认证相关路由
package routers

import (
	"DunshanOps/handlers"
	"DunshanOps/middleware"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes 设置认证相关路由
// r: gin 路由组实例
func SetupAuthRoutes(r *gin.RouterGroup) {
	// 创建认证处理器实例
	authHandler := handlers.NewAuthHandler()

	// 认证路由组
	authGroup := r.Group("/auth")
	{
		// POST /api/auth/login - 用户登录接口
		authGroup.POST("/login", authHandler.Login)

		// GET /api/auth/providers - 获取可用认证提供者
		authGroup.GET("/providers", authHandler.GetProviders)

		// GET /api/auth/profile - 获取当前登录用户信息（需要JWT认证）
		authGroup.GET("/profile", middleware.JWTAuth(), authHandler.GetCurrentUser)

		// 其他认证相关路由可以在这里添加
		// authGroup.POST("/logout", authHandler.Logout)       // 用户登出
		// authGroup.POST("/refresh", authHandler.RefreshToken) // 刷新令牌
	}
}
