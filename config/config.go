package config

import (
	"fmt"

	"DunshanOps/database"
	"DunshanOps/logger"

	"github.com/spf13/viper"
)

const (
	// GIN 服务默认端口
	GinServerDefaultPort = 8080
	// GIN 服务默认主机
	GinServerDefaultHost = "0.0.0.0"
	// GIN 服务默认模式
	GinServerDefaultMode = "release"
)

// ServerConfig 服务器配置结构体
type ServerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

// AppConfig 应用配置结构体
type AppConfig struct {
	Server   ServerConfig         `mapstructure:"server"`
	Log      logger.LogConfig     `mapstructure:"log"`
	Database database.MongoConfig `mapstructure:"database"`
}

// 全局配置实例
var AppConf AppConfig

// 使用 viper 管理配置文件
// InitConfig 初始化配置系统
// path: 配置文件的路径
func InitConfig(path string) error {
	// 设置默认值
	setDefaults()

	// 设置配置文件路径
	viper.SetConfigFile(path)

	// 设置配置文件的格式为 YAML
	viper.SetConfigType("yaml")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，使用默认值
			fmt.Printf("未找到配置文件: %s，将使用默认配置\n", path)
		} else {
			// 配置文件格式错误，退出程序
			return fmt.Errorf("配置文件格式错误: %w", err)
		}
	} else {
		fmt.Printf("成功加载配置文件: %s\n", path)
	}

	// 将配置绑定到结构体
	if err := viper.Unmarshal(&AppConf); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	return nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器配置默认值
	viper.SetDefault("server.host", GinServerDefaultHost)
	viper.SetDefault("server.port", GinServerDefaultPort)
	viper.SetDefault("server.mode", GinServerDefaultMode)

	// 日志 Logger 配置默认值
	viper.SetDefault("log.level", logger.DefaultLogLevel)
	viper.SetDefault("log.app_log_path", logger.DefaultAPPLogPath)
	viper.SetDefault("log.access_log_path", logger.DefaultAccessLogPath)
	viper.SetDefault("log.max_size", logger.DefaultLogMaxSize)
	viper.SetDefault("log.max_backups", logger.DefaultLogMaxBackups)
	viper.SetDefault("log.max_age", logger.DefaultLogMaxAge)
	viper.SetDefault("log.compress", logger.DefaultLogCompress)

	// MongoDB 数据库配置默认值
	viper.SetDefault("database.timeout", database.DefaultMongoTimeout)
	viper.SetDefault("database.auth", database.DefaultMongoAuth)
}

// GetServerAddr 获取服务器地址
func GetServerAddr() string {
	return fmt.Sprintf("%s:%d", AppConf.Server.Host, AppConf.Server.Port)
}

// GetServerMode 获取服务器模式
func GetServerMode() string {
	return AppConf.Server.Mode
}

// IsDebug 是否开启调试模式
func IsDebug() bool {
	return AppConf.Server.Mode == "debug"
}
